import React, { useState } from 'react';
import { Loading<PERSON>utton, SaveButton, PublishButton, AutoSaveIndicator } from '@/components/ui/LoadingButton';
import { Save, Globe } from 'lucide-react';

/**
 * Test component to demonstrate the enhanced loading buttons and success feedback
 */
export const LoadingButtonTest: React.FC = () => {
  const [saveLoading, setSaveLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [publishSuccess, setPublishSuccess] = useState(false);
  const [autoSaveLoading, setAutoSaveLoading] = useState(false);
  const [autoSaveSuccess, setAutoSaveSuccess] = useState(false);

  const handleSave = async () => {
    setSaveLoading(true);
    setSaveSuccess(false);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setSaveLoading(false);
    setSaveSuccess(true);
    
    // Auto-clear success state
    setTimeout(() => setSaveSuccess(false), 3000);
  };

  const handlePublish = async () => {
    setPublishLoading(true);
    setPublishSuccess(false);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setPublishLoading(false);
    setPublishSuccess(true);
    
    // Auto-clear success state
    setTimeout(() => setPublishSuccess(false), 4000);
  };

  const handleAutoSave = async () => {
    setAutoSaveLoading(true);
    setAutoSaveSuccess(false);
    
    // Simulate auto-save
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setAutoSaveLoading(false);
    setAutoSaveSuccess(true);
    
    // Auto-clear success state
    setTimeout(() => setAutoSaveSuccess(false), 2000);
  };

  return (
    <div className="p-8 space-y-8 max-w-2xl mx-auto">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Loading Button Test</h1>
        <p className="text-gray-600">
          Test the enhanced user experience for page publishing with loading animations and visual feedback.
        </p>
      </div>

      <div className="space-y-6">
        {/* Save Button Test */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Save Button</h2>
          <div className="flex gap-4 items-center">
            <SaveButton
              onClick={handleSave}
              loading={saveLoading}
              success={saveSuccess}
              icon={<Save className="h-4 w-4" />}
            >
              Save Changes
            </SaveButton>
            <span className="text-sm text-gray-500">
              {saveLoading && "Saving..."}
              {saveSuccess && "✅ Saved successfully!"}
            </span>
          </div>
        </div>

        {/* Publish Button Test */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Publish Button</h2>
          <div className="flex gap-4 items-center">
            <PublishButton
              onClick={handlePublish}
              loading={publishLoading}
              success={publishSuccess}
              icon={<Globe className="h-4 w-4" />}
            >
              Publish Page
            </PublishButton>
            <span className="text-sm text-gray-500">
              {publishLoading && "Publishing..."}
              {publishSuccess && "✅ Published successfully!"}
            </span>
          </div>
        </div>

        {/* Auto-Save Indicator Test */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Auto-Save Indicator</h2>
          <div className="flex gap-4 items-center">
            <button
              onClick={handleAutoSave}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              disabled={autoSaveLoading}
            >
              Trigger Auto-Save
            </button>
            <AutoSaveIndicator
              loading={autoSaveLoading}
              success={autoSaveSuccess}
              lastSaved={autoSaveSuccess ? new Date().toISOString() : null}
            />
          </div>
        </div>

        {/* Generic Loading Button Test */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">Generic Loading Button</h2>
          <div className="flex gap-4">
            <LoadingButton
              loading={false}
              success={false}
              variant="outline"
            >
              Normal State
            </LoadingButton>
            <LoadingButton
              loading={true}
              loadingText="Processing..."
              variant="default"
            >
              Loading State
            </LoadingButton>
            <LoadingButton
              loading={false}
              success={true}
              successText="Completed!"
              variant="default"
            >
              Success State
            </LoadingButton>
          </div>
        </div>
      </div>

      <div className="border-t pt-6">
        <h2 className="text-lg font-semibold mb-4">Features Demonstrated</h2>
        <ul className="space-y-2 text-sm text-gray-600">
          <li>✅ Loading spinners with disabled state during API calls</li>
          <li>✅ Custom loading text for different operations</li>
          <li>✅ Success indicators with checkmarks and custom text</li>
          <li>✅ Auto-clearing success states after specified duration</li>
          <li>✅ Specialized save and publish button variants</li>
          <li>✅ Auto-save indicator with last saved timestamp</li>
          <li>✅ Proper state management and visual feedback</li>
        </ul>
      </div>
    </div>
  );
};

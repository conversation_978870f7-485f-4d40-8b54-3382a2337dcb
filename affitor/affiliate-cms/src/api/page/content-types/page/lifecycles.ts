/**
 * Page lifecycle hooks
 * Handles automatic referrer link generation when pages are published
 */

export default {
  /**
   * After updating a page, check if status changed to published and create referrer link
   */
  async afterUpdate(event) {
    const { data, where } = event.params;
    const { result } = event;

    // Add debug logging to see if lifecycle is triggered
    strapi.log.info('Page lifecycle afterUpdate triggered', {
      data,
      where,
      hasResult: !!result
    });

    try {
      // Only proceed if page status was updated to 'published'
      if (data.status !== 'published') {
        strapi.log.info('Page status not published, skipping referrer link creation', {
          status: data.status
        });
        return;
      }

      strapi.log.info('Page published, checking for referrer link creation', {
        pageId: where.id,
        documentId: where.documentId,
        status: data.status
      });

      // Get the updated page with author information
      const page = await strapi.documents('api::page.page').findOne({
        documentId: where.documentId,
        populate: ['author']
      });

      if (!page || !page.author) {
        strapi.log.warn('Page or author not found for referrer link creation', {
          pageId: where.id,
          documentId: where.documentId
        });
        return;
      }

      // Get the referrer for this user
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: { user: { id: page.author.id } },
        fields: ['id', 'referral_code']
      });

      if (!referrers || referrers.length === 0) {
        strapi.log.info('No referrer found for user, skipping referrer link creation', {
          userId: page.author.id,
          pageId: page.id
        });
        return;
      }

      const referrer = referrers[0];

      // Call the referrer link service to create the automatic link
      const referrerLinkService = strapi.service('api::referrer-link.referrer-link');
      await referrerLinkService.createAutomaticReferrerLink(page, referrer);

      strapi.log.info('Automatic referrer link created for published page', {
        pageId: page.id,
        pageSlug: page.slug,
        referrerId: referrer.id,
        referralCode: referrer.referral_code
      });

    } catch (error) {
      strapi.log.error('Error creating automatic referrer link for published page:', error);
      // Don't throw the error to avoid breaking the page update
    }
  }
};
